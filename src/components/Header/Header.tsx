import { useSpeechOutput } from "../../contexts/SpeechProvider";
import IconGoBack from "../Buttons/GoBack/IconGoBack";
import IconMenu from "../Buttons/Menu/IconMenu";
import IconMicrophoneOn from "../Buttons/MicrophoneOn/IconMicrophoneOn";
import IconMicrophoneOff from "../Buttons/MicrophoneOff/IconMicrophoneOff";
import { ControlButton, SafeAreaHome } from "microapps";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isAzureTTSEnabled },
    pauseMusic,
    resumeMusic,
    toggleAzureTTS,
  } = useSpeechOutput();

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleMusicClick = () => {
    if (isMusicPlaying) {
      pauseMusic();
    } else {
      resumeMusic();
    }
  };

  const handleSpeechClick = () => {
    const newState = toggleAzureTTS();
    console.log(`Azure TTS ${newState ? 'activado' : 'desactivado'}`);
  };

  return (
    <>
      <div className="header">
        <div className="header-left">
          {currentView === "main" && <IconMenu />}

          {showBackButton && currentView !== "main" && onBackToMain && (
            <div className="back-button" onClick={onBackToMain}>
              <IconGoBack />
            </div>
          )}
        </div>

        <div className="header-title">{renderTitle()}</div>

        <div className="header-right">
          <div>
            <ControlButton
              onClick={handleMusicClick}
              type="music"
              isActive={ !audioState.isMuted && isMusicPlaying}
              size="big"
            />

          </div>

          <div
            className={`sound-icon speech-control ${isAzureTTSEnabled ? "active" : "disabled"}`}
            onClick={handleSpeechClick}
            title={
              isAzureTTSEnabled
                ? "Azure TTS activado - Click para desactivar"
                : "Azure TTS desactivado - Click para activar"
            }
          >
            {isAzureTTSEnabled ? (
              <IconMicrophoneOn />
            ) : (
              <IconMicrophoneOff />
            )}
          </div>

          <div className="home-icon">
            <SafeAreaHome isVisible={false} />
          </div>
        </div>
      </div>

    </>
  );
};
